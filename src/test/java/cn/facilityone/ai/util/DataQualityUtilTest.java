package cn.facilityone.ai.util;

import cn.facilityone.ai.dto.WorkOrderData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据质量检查工具类测试
 */
class DataQualityUtilTest {

    private List<WorkOrderData> testWorkOrderList;

    @BeforeEach
    void setUp() {
        testWorkOrderList = createTestWorkOrderList();
    }

    /**
     * 测试数据质量检查
     */
    @Test
    void testCheckDataQuality() {
        List<DataQualityUtil.DataQualityIssue> issues = DataQualityUtil.checkDataQuality(testWorkOrderList);

        assertNotNull(issues);
        assertTrue(issues.size() > 0, "应该检测到数据质量问题");

        // 验证检测到的问题类型
        boolean hasMissingField = issues.stream()
                .anyMatch(issue -> issue.getIssueType() == DataQualityUtil.DataQualityIssueType.MISSING_REQUIRED_FIELD);
        boolean hasLogicalError = issues.stream()
                .anyMatch(issue -> issue.getIssueType() == DataQualityUtil.DataQualityIssueType.LOGICAL_ERROR);

        assertTrue(hasMissingField, "应该检测到必需字段缺失问题");
        assertTrue(hasLogicalError, "应该检测到逻辑错误问题");
    }

    /**
     * 测试空数据列表
     */
    @Test
    void testCheckDataQuality_EmptyList() {
        List<DataQualityUtil.DataQualityIssue> issues = DataQualityUtil.checkDataQuality(new ArrayList<>());
        assertTrue(issues.isEmpty(), "空数据列表应该返回空问题列表");
    }

    /**
     * 测试质量报告摘要生成
     */
    @Test
    void testGenerateQualityReportSummary() {
        List<DataQualityUtil.DataQualityIssue> issues = DataQualityUtil.checkDataQuality(testWorkOrderList);
        String summary = DataQualityUtil.generateQualityReportSummary(issues);

        assertNotNull(summary);
        assertFalse(summary.isEmpty());
        assertTrue(summary.contains("数据质量问题"));
    }

    /**
     * 测试无问题数据的报告摘要
     */
    @Test
    void testGenerateQualityReportSummary_NoIssues() {
        List<DataQualityUtil.DataQualityIssue> emptyIssues = new ArrayList<>();
        String summary = DataQualityUtil.generateQualityReportSummary(emptyIssues);

        assertNotNull(summary);
        assertTrue(summary.contains("数据质量良好"));
    }

    /**
     * 测试必需字段检查
     */
    @Test
    void testRequiredFieldsCheck() {
        // 创建缺少必需字段的工单
        WorkOrderData incompleteWorkOrder = new WorkOrderData();
        incompleteWorkOrder.setOrderNumber(""); // 工单号为空
        incompleteWorkOrder.setApplicant(""); // 申请人为空
        // 不设置创建日期

        List<WorkOrderData> incompleteList = List.of(incompleteWorkOrder);
        List<DataQualityUtil.DataQualityIssue> issues = DataQualityUtil.checkDataQuality(incompleteList);

        // 应该检测到3个必需字段缺失问题
        long missingFieldCount = issues.stream()
                .filter(issue -> issue.getIssueType() == DataQualityUtil.DataQualityIssueType.MISSING_REQUIRED_FIELD)
                .count();

        assertEquals(3, missingFieldCount, "应该检测到3个必需字段缺失问题");
    }

    /**
     * 测试逻辑错误检查
     */
    @Test
    void testLogicalErrorCheck() {
        // 创建有逻辑错误的工单
        WorkOrderData logicalErrorWorkOrder = new WorkOrderData();
        logicalErrorWorkOrder.setOrderNumber("WO_LOGIC_ERROR");
        logicalErrorWorkOrder.setApplicant("测试申请人");
        logicalErrorWorkOrder.setCreateDate(LocalDate.now());
        logicalErrorWorkOrder.setActualStartTime(LocalTime.of(15, 0)); // 开始时间晚于结束时间
        logicalErrorWorkOrder.setActualEndTime(LocalTime.of(14, 0));

        List<WorkOrderData> errorList = List.of(logicalErrorWorkOrder);
        List<DataQualityUtil.DataQualityIssue> issues = DataQualityUtil.checkDataQuality(errorList);

        // 应该检测到逻辑错误
        boolean hasLogicalError = issues.stream()
                .anyMatch(issue -> issue.getIssueType() == DataQualityUtil.DataQualityIssueType.LOGICAL_ERROR);

        assertTrue(hasLogicalError, "应该检测到逻辑错误");
    }

    /**
     * 创建测试用的工单数据列表
     */
    private List<WorkOrderData> createTestWorkOrderList() {
        List<WorkOrderData> workOrders = new ArrayList<>();

        // 正常工单
        for (int i = 1; i <= 5; i++) {
            WorkOrderData wo = new WorkOrderData();
            wo.setOrderNumber("WO" + String.format("%03d", i));
            wo.setApplicant("申请人" + i);
            wo.setCreateDate(LocalDate.now().minusDays(i));
            wo.setCreateTime(LocalTime.of(9, 0));
            wo.setActualStartTime(LocalTime.of(10, 0));
            wo.setActualEndTime(LocalTime.of(11, 0));
            wo.setWorkDurationMinutes(60);
            wo.setStatus("已完成");
            workOrders.add(wo);
        }

        // 添加有数据质量问题的工单
        
        // 1. 缺少必需字段的工单
        WorkOrderData missingFieldWo = new WorkOrderData();
        missingFieldWo.setOrderNumber(""); // 工单号为空
        missingFieldWo.setApplicant(""); // 申请人为空
        // 不设置创建日期
        workOrders.add(missingFieldWo);

        // 2. 时间逻辑错误的工单
        WorkOrderData timeErrorWo = new WorkOrderData();
        timeErrorWo.setOrderNumber("WO_TIME_ERROR");
        timeErrorWo.setApplicant("申请人TimeError");
        timeErrorWo.setCreateDate(LocalDate.now());
        timeErrorWo.setActualStartTime(LocalTime.of(15, 0)); // 开始时间晚于结束时间
        timeErrorWo.setActualEndTime(LocalTime.of(14, 0));
        workOrders.add(timeErrorWo);

        // 3. 数据不一致的工单
        WorkOrderData inconsistentWo = new WorkOrderData();
        inconsistentWo.setOrderNumber("WO_INCONSISTENT");
        inconsistentWo.setApplicant("申请人Inconsistent");
        inconsistentWo.setCreateDate(LocalDate.now());
        inconsistentWo.setActualStartTime(LocalTime.of(10, 0));
        inconsistentWo.setActualEndTime(LocalTime.of(12, 0)); // 实际2小时
        inconsistentWo.setWorkDurationMinutes(30); // 记录30分钟，差异很大
        workOrders.add(inconsistentWo);

        return workOrders;
    }
}

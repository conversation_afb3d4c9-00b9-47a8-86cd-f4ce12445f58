package cn.facilityone.ai.service.tool;

import cn.facilityone.ai.dto.FmWorkOrderExportDTO;
import cn.facilityone.ai.dto.WorkOrderExportResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FmWoTool 测试类
 */
@ExtendWith(MockitoExtension.class)
class FmWoToolTest {

    @InjectMocks
    private FmWoTool fmWoTool;

    @Test
    void testExportWorkOrders_WithNullParameter() {
        // 测试空参数
        WorkOrderExportResponse result = fmWoTool.exportWorkOrders(null);
        assertNotNull(result);
        assertEquals("500", result.getCode());
        assertEquals("参数错误，缺少必要参数", result.getMessage());
        assertFalse(result.isSuccess());
    }

    @Test
    void testExportWorkOrders_WithMissingUserId() {
        // 测试缺少用户ID
        FmWorkOrderExportDTO dto = new FmWorkOrderExportDTO();
        dto.setProjectId(1L);
        dto.setStartDate("2025-01-01");
        dto.setEndDate("2025-01-31");

        WorkOrderExportResponse result = fmWoTool.exportWorkOrders(dto);
        assertNotNull(result);
        assertEquals("500", result.getCode());
        assertEquals("参数错误，缺少搜索人ID", result.getMessage());
        assertFalse(result.isSuccess());
    }

    @Test
    void testExportWorkOrders_WithMissingProjectId() {
        // 测试缺少项目ID
        FmWorkOrderExportDTO dto = new FmWorkOrderExportDTO();
        dto.setUserId(1L);
        dto.setStartDate("2025-01-01");
        dto.setEndDate("2025-01-31");

        WorkOrderExportResponse result = fmWoTool.exportWorkOrders(dto);
        assertNotNull(result);
        assertEquals("500", result.getCode());
        assertEquals("参数错误，缺少项目ID", result.getMessage());
        assertFalse(result.isSuccess());
    }

    @Test
    void testExportWorkOrders_WithMissingStartDate() {
        // 测试缺少开始日期
        FmWorkOrderExportDTO dto = new FmWorkOrderExportDTO();
        dto.setUserId(1L);
        dto.setProjectId(1L);
        dto.setEndDate("2025-01-31");

        WorkOrderExportResponse result = fmWoTool.exportWorkOrders(dto);
        assertNotNull(result);
        assertEquals("500", result.getCode());
        assertEquals("参数错误，缺少开始日期", result.getMessage());
        assertFalse(result.isSuccess());
    }

    @Test
    void testExportWorkOrders_WithMissingEndDate() {
        // 测试缺少结束日期
        FmWorkOrderExportDTO dto = new FmWorkOrderExportDTO();
        dto.setUserId(1L);
        dto.setProjectId(1L);
        dto.setStartDate("2025-01-01");

        WorkOrderExportResponse result = fmWoTool.exportWorkOrders(dto);
        assertNotNull(result);
        assertEquals("500", result.getCode());
        assertEquals("参数错误，缺少结束日期", result.getMessage());
        assertFalse(result.isSuccess());
    }

    @Test
    void testExportWorkOrders_WithInvalidDateFormat() {
        // 测试无效的日期格式
        FmWorkOrderExportDTO dto = new FmWorkOrderExportDTO();
        dto.setUserId(1L);
        dto.setProjectId(1L);
        dto.setStartDate("2025/01/01"); // 错误格式
        dto.setEndDate("2025-01-31");

        WorkOrderExportResponse result = fmWoTool.exportWorkOrders(dto);
        assertNotNull(result);
        assertEquals("500", result.getCode());
        assertEquals("参数错误，日期格式不正确，应为YYYY-MM-DD格式", result.getMessage());
        assertFalse(result.isSuccess());
    }

    @Test
    void testIsValidDateFormat() {
        // 使用反射测试私有方法
        try {
            java.lang.reflect.Method method = FmWoTool.class.getDeclaredMethod("isValidDateFormat", String.class);
            method.setAccessible(true);
            
            // 测试有效格式
            assertTrue((Boolean) method.invoke(fmWoTool, "2025-01-01"));
            assertTrue((Boolean) method.invoke(fmWoTool, "2025-12-31"));
            
            // 测试无效格式
            assertFalse((Boolean) method.invoke(fmWoTool, "2025/01/01"));
            assertFalse((Boolean) method.invoke(fmWoTool, "25-01-01"));
            assertFalse((Boolean) method.invoke(fmWoTool, "2025-1-1"));
            assertFalse((Boolean) method.invoke(fmWoTool, ""));
            assertFalse((Boolean) method.invoke(fmWoTool, (String) null));
            
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }
}

package cn.facilityone.ai.controller;

import cn.facilityone.ai.service.tool.WeatherToolService;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/12 11:25
 */


@RestController
public class OpenAIController {

    private final WeatherToolService weatherToolService;
    private final ChatClient chatClient;

    public OpenAIController(ChatClient chatClient, WeatherToolService weatherToolService) {
        this.chatClient = chatClient;
        this.weatherToolService = weatherToolService;
    }


    // 原始同步接口：一次性返回完整回复
    @GetMapping("/ai")
    public String ollama(@RequestParam String msg) {
        String content = chatClient.prompt().user(msg).tools(weatherToolService).call().content();
        System.out.println(content);
        return content;
    }

    @GetMapping(value = "/ai/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> streamResponse(@RequestParam String msg) {
        Flux<String> content = chatClient.prompt().user(msg).tools(weatherToolService).stream().content();
        System.out.println(content);
        return content
                .map(response -> new String(response.getBytes(), StandardCharsets.UTF_8));
    }

}
package cn.facilityone.ai.util;

import cn.facilityone.ai.dto.OutlierData;
import cn.facilityone.ai.dto.WorkOrderData;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 离群数据检测工具类
 * 提供多种离群数据检测算法
 */
@Slf4j
public class OutlierDetectionUtil {

    // 默认的Z-Score阈值（调整为更严格的标准）
    private static final double DEFAULT_Z_SCORE_THRESHOLD = 3.0;

    // 默认的IQR倍数（调整为更严格的标准）
    private static final double DEFAULT_IQR_MULTIPLIER = 2.0;

    // 最小数据量要求
    private static final int MIN_DATA_SIZE_FOR_IQR = 10;  // IQR方法至少需要10个数据点
    private static final int MIN_DATA_SIZE_FOR_ZSCORE = 15; // Z-Score方法至少需要15个数据点

    /**
     * 检测处理时长离群数据（使用IQR方法）
     *
     * @param workOrderList 工单数据列表
     * @return 离群数据列表
     */
    public static List<OutlierData> detectProcessingTimeOutliers(List<WorkOrderData> workOrderList) {
        if (CollectionUtil.isEmpty(workOrderList)) {
            return new ArrayList<>();
        }

        // 提取有效的处理时长数据
        List<WorkOrderData> validWorkOrders = workOrderList.stream()
                .filter(wo -> wo.getWorkDurationMinutes() != null && wo.getWorkDurationMinutes() > 0)
                .collect(Collectors.toList());

        if (validWorkOrders.size() < MIN_DATA_SIZE_FOR_IQR) {
            log.debug("处理时长数据不足，无法进行离群检测，有效数据量: {}，最少需要: {}",
                    validWorkOrders.size(), MIN_DATA_SIZE_FOR_IQR);
            return new ArrayList<>();
        }

        // 计算IQR
        List<Integer> processingTimes = validWorkOrders.stream()
                .map(WorkOrderData::getWorkDurationMinutes)
                .sorted()
                .collect(Collectors.toList());

        double q1 = calculatePercentile(processingTimes, 25);
        double q3 = calculatePercentile(processingTimes, 75);
        double iqr = q3 - q1;

        // 动态调整IQR倍数：数据量越小，标准越严格
        double iqrMultiplier = calculateDynamicIQRMultiplier(validWorkOrders.size());
        double lowerBound = q1 - iqrMultiplier * iqr;
        double upperBound = q3 + iqrMultiplier * iqr;

        log.debug("处理时长IQR分析 - Q1: {}, Q3: {}, IQR: {}, 倍数: {}, 下界: {}, 上界: {}",
                q1, q3, iqr, iqrMultiplier, lowerBound, upperBound);

        // 检测离群数据
        List<OutlierData> outliers = new ArrayList<>();
        for (WorkOrderData workOrder : validWorkOrders) {
            Integer duration = workOrder.getWorkDurationMinutes();
            if (duration < lowerBound || duration > upperBound) {
                OutlierData outlier = new OutlierData();
                outlier.setWorkOrder(workOrder);
                outlier.setOutlierType(OutlierData.OutlierType.PROCESSING_TIME_OUTLIER);
                outlier.setAnomalyField("workDurationMinutes");
                outlier.setAnomalyValue(duration);
                
                // 计算离群程度
                double outlierScore = duration < lowerBound ? 
                        (lowerBound - duration) / iqr : (duration - upperBound) / iqr;
                outlier.setOutlierScore(outlierScore);
                
                // 生成原因描述
                String reason = duration < lowerBound ? 
                        String.format("处理时长过短: %d分钟 (正常范围: %.1f-%.1f分钟)", duration, lowerBound, upperBound) :
                        String.format("处理时长过长: %d分钟 (正常范围: %.1f-%.1f分钟)", duration, lowerBound, upperBound);
                outlier.setReason(reason);
                outlier.setNormalRange(String.format("%.1f-%.1f分钟", lowerBound, upperBound));
                
                outliers.add(outlier);
            }
        }

        log.info("处理时长离群检测完成，检测到 {} 个离群数据", outliers.size());
        return outliers;
    }

    /**
     * 检测响应时间离群数据
     *
     * @param workOrderList 工单数据列表
     * @return 离群数据列表
     */
    public static List<OutlierData> detectResponseTimeOutliers(List<WorkOrderData> workOrderList) {
        if (CollectionUtil.isEmpty(workOrderList)) {
            return new ArrayList<>();
        }

        // 计算响应时间（从创建时间到接单时间）
        List<WorkOrderData> validWorkOrders = workOrderList.stream()
                .filter(wo -> wo.getCreateTime() != null && wo.getAcceptTime() != null)
                .collect(Collectors.toList());

        if (validWorkOrders.size() < MIN_DATA_SIZE_FOR_ZSCORE) {
            log.debug("响应时间数据不足，无法进行离群检测，有效数据量: {}，最少需要: {}",
                    validWorkOrders.size(), MIN_DATA_SIZE_FOR_ZSCORE);
            return new ArrayList<>();
        }

        // 计算响应时间（分钟）
        List<Long> responseTimes = validWorkOrders.stream()
                .map(wo -> ChronoUnit.MINUTES.between(wo.getCreateTime(), wo.getAcceptTime()))
                .filter(time -> time >= 0) // 过滤负值
                .sorted()
                .collect(Collectors.toList());

        if (responseTimes.size() < MIN_DATA_SIZE_FOR_ZSCORE) {
            log.debug("响应时间有效数据不足，无法进行离群检测，有效数据量: {}", responseTimes.size());
            return new ArrayList<>();
        }

        // 使用Z-Score方法检测离群数据
        double mean = responseTimes.stream().mapToLong(Long::longValue).average().orElse(0.0);
        double stdDev = calculateStandardDeviation(responseTimes, mean);

        // 动态调整Z-Score阈值：数据量越小，标准越严格
        double zScoreThreshold = calculateDynamicZScoreThreshold(responseTimes.size());

        List<OutlierData> outliers = new ArrayList<>();
        for (WorkOrderData workOrder : validWorkOrders) {
            long responseTime = ChronoUnit.MINUTES.between(workOrder.getCreateTime(), workOrder.getAcceptTime());
            if (responseTime >= 0) {
                double zScore = Math.abs((responseTime - mean) / stdDev);
                if (zScore > zScoreThreshold) {
                    OutlierData outlier = new OutlierData();
                    outlier.setWorkOrder(workOrder);
                    outlier.setOutlierType(OutlierData.OutlierType.RESPONSE_TIME_OUTLIER);
                    outlier.setAnomalyField("responseTime");
                    outlier.setAnomalyValue(responseTime);
                    outlier.setOutlierScore(zScore);
                    
                    String reason = String.format("响应时间异常: %d分钟 (平均: %.1f分钟, 标准差: %.1f, Z-Score: %.2f)",
                            responseTime, mean, stdDev, zScore);
                    outlier.setReason(reason);
                    outlier.setNormalRange(String.format("%.1f±%.1f分钟", mean, stdDev * zScoreThreshold));
                    
                    outliers.add(outlier);
                }
            }
        }

        log.info("响应时间离群检测完成，检测到 {} 个离群数据", outliers.size());
        return outliers;
    }

    /**
     * 检测业务规则离群数据
     * 注意：只检测真正的逻辑异常，不包括数据完整性问题
     *
     * @param workOrderList 工单数据列表
     * @return 离群数据列表
     */
    public static List<OutlierData> detectBusinessRuleOutliers(List<WorkOrderData> workOrderList) {
        if (CollectionUtil.isEmpty(workOrderList)) {
            return new ArrayList<>();
        }

        // 业务规则检测也需要一定的数据量基础
        if (workOrderList.size() < 5) {
            log.debug("数据量过小，跳过业务规则离群检测，数据量: {}", workOrderList.size());
            return new ArrayList<>();
        }

        List<OutlierData> outliers = new ArrayList<>();

        for (WorkOrderData workOrder : workOrderList) {
            // 只检测严重的时间逻辑异常
            outliers.addAll(detectSevereTimeLogicAnomalies(workOrder));

            // 检测严重的状态逻辑异常
            outliers.addAll(detectSevereStatusAnomalies(workOrder));
        }

        log.info("业务规则离群检测完成，检测到 {} 个离群数据", outliers.size());
        return outliers;
    }

    /**
     * 计算百分位数
     */
    private static double calculatePercentile(List<Integer> sortedData, double percentile) {
        if (sortedData.isEmpty()) {
            return 0.0;
        }
        
        double index = (percentile / 100.0) * (sortedData.size() - 1);
        int lowerIndex = (int) Math.floor(index);
        int upperIndex = (int) Math.ceil(index);
        
        if (lowerIndex == upperIndex) {
            return sortedData.get(lowerIndex);
        }
        
        double weight = index - lowerIndex;
        return sortedData.get(lowerIndex) * (1 - weight) + sortedData.get(upperIndex) * weight;
    }

    /**
     * 计算标准差
     */
    private static double calculateStandardDeviation(List<Long> data, double mean) {
        if (data.size() <= 1) {
            return 0.0;
        }
        
        double sumSquaredDiffs = data.stream()
                .mapToDouble(value -> Math.pow(value - mean, 2))
                .sum();
        
        return Math.sqrt(sumSquaredDiffs / (data.size() - 1));
    }

    /**
     * 检测严重的时间逻辑异常
     * 只检测明显违反逻辑的时间关系
     */
    private static List<OutlierData> detectSevereTimeLogicAnomalies(WorkOrderData workOrder) {
        List<OutlierData> outliers = new ArrayList<>();

        // 检测实际开始时间晚于实际结束时间（这是明显的逻辑错误）
        if (workOrder.getActualStartTime() != null && workOrder.getActualEndTime() != null) {
            if (workOrder.getActualStartTime().isAfter(workOrder.getActualEndTime())) {
                OutlierData outlier = createBusinessRuleOutlier(workOrder,
                        "timeLogic", "实际开始时间晚于实际结束时间",
                        "严重时间逻辑错误：开始时间不能晚于结束时间");
                outliers.add(outlier);
            }
        }

        // 检测工作时长与实际时间严重不匹配（超过2小时差异才算异常）
        if (workOrder.getWorkDurationMinutes() != null &&
            workOrder.getActualStartTime() != null &&
            workOrder.getActualEndTime() != null) {

            long actualDuration = ChronoUnit.MINUTES.between(
                    workOrder.getActualStartTime(), workOrder.getActualEndTime());

            // 只有差异超过2小时（120分钟）才认为是严重异常
            if (Math.abs(actualDuration - workOrder.getWorkDurationMinutes()) > 120) {
                OutlierData outlier = createBusinessRuleOutlier(workOrder,
                        "timeLogic", "工作时长与实际时间严重不匹配",
                        String.format("严重时间不一致：记录时长(%d分钟)与实际时长(%d分钟)差异超过2小时",
                                workOrder.getWorkDurationMinutes(), actualDuration));
                outliers.add(outlier);
            }
        }

        return outliers;
    }

    /**
     * 检测严重的状态逻辑异常
     * 只检测明显违反业务逻辑的状态问题
     */
    private static List<OutlierData> detectSevereStatusAnomalies(WorkOrderData workOrder) {
        List<OutlierData> outliers = new ArrayList<>();

        // 只检测明显的状态逻辑矛盾，且需要有明确的时间信息
        if (StrUtil.isNotBlank(workOrder.getStatus()) &&
            workOrder.getActualStartTime() != null) {

            String status = workOrder.getStatus().toLowerCase();

            // 只有当状态明确为"已完成"且有开始时间但无结束时间时才认为是异常
            if ((status.contains("已完成") || status.contains("已结束") || status.contains("已关闭"))
                && workOrder.getActualEndTime() == null) {
                OutlierData outlier = createBusinessRuleOutlier(workOrder,
                        "statusAnomaly", "已完成状态但无结束时间",
                        "严重状态异常：工单状态为已完成但缺少实际结束时间");
                outliers.add(outlier);
            }

            // 只有当状态明确为"进行中"且已有结束时间时才认为是异常
            if ((status.contains("进行中") || status.contains("处理中"))
                && workOrder.getActualEndTime() != null) {
                OutlierData outlier = createBusinessRuleOutlier(workOrder,
                        "statusAnomaly", "进行中状态但有结束时间",
                        "严重状态异常：工单状态为进行中但已有实际结束时间");
                outliers.add(outlier);
            }
        }

        return outliers;
    }

    /**
     * 动态计算IQR倍数
     * 数据量越小，标准越严格，减少误报
     *
     * @param dataSize 数据量大小
     * @return IQR倍数
     */
    private static double calculateDynamicIQRMultiplier(int dataSize) {
        if (dataSize < 20) {
            return 2.5;  // 小样本使用更严格的标准
        } else if (dataSize < 50) {
            return 2.2;
        } else if (dataSize < 100) {
            return 2.0;
        } else {
            return DEFAULT_IQR_MULTIPLIER; // 大样本使用默认标准
        }
    }

    /**
     * 动态计算Z-Score阈值
     * 数据量越小，标准越严格，减少误报
     *
     * @param dataSize 数据量大小
     * @return Z-Score阈值
     */
    private static double calculateDynamicZScoreThreshold(int dataSize) {
        if (dataSize < 30) {
            return 3.5;  // 小样本使用更严格的标准
        } else if (dataSize < 50) {
            return 3.2;
        } else if (dataSize < 100) {
            return 3.0;
        } else {
            return DEFAULT_Z_SCORE_THRESHOLD; // 大样本使用默认标准
        }
    }

    /**
     * 创建业务规则离群数据对象
     */
    private static OutlierData createBusinessRuleOutlier(WorkOrderData workOrder,
                                                        String anomalyField,
                                                        Object anomalyValue,
                                                        String reason) {
        OutlierData outlier = new OutlierData();
        outlier.setWorkOrder(workOrder);
        outlier.setOutlierType(OutlierData.OutlierType.BUSINESS_RULE_OUTLIER);
        outlier.setAnomalyField(anomalyField);
        outlier.setAnomalyValue(anomalyValue);
        outlier.setReason(reason);
        outlier.setOutlierScore(1.0); // 业务规则异常统一设为1.0
        return outlier;
    }
}

package cn.facilityone.ai.util;

import cn.facilityone.ai.dto.WorkOrderData;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据质量检查工具类
 * 用于检查工单数据的完整性和一致性问题
 * 注意：这里检查的是数据质量问题，不是统计意义上的离群数据
 */
@Slf4j
public class DataQualityUtil {

    /**
     * 数据质量问题类型
     */
    public enum DataQualityIssueType {
        MISSING_REQUIRED_FIELD("必需字段缺失"),
        INVALID_DATA_FORMAT("数据格式错误"),
        DATA_INCONSISTENCY("数据不一致"),
        LOGICAL_ERROR("逻辑错误");

        private final String description;

        DataQualityIssueType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 数据质量问题记录
     */
    public static class DataQualityIssue {
        private WorkOrderData workOrder;
        private DataQualityIssueType issueType;
        private String fieldName;
        private String description;
        private String suggestion;

        // Getters and Setters
        public WorkOrderData getWorkOrder() { return workOrder; }
        public void setWorkOrder(WorkOrderData workOrder) { this.workOrder = workOrder; }
        
        public DataQualityIssueType getIssueType() { return issueType; }
        public void setIssueType(DataQualityIssueType issueType) { this.issueType = issueType; }
        
        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public String getSuggestion() { return suggestion; }
        public void setSuggestion(String suggestion) { this.suggestion = suggestion; }
    }

    /**
     * 检查工单数据质量
     *
     * @param workOrderList 工单数据列表
     * @return 数据质量问题列表
     */
    public static List<DataQualityIssue> checkDataQuality(List<WorkOrderData> workOrderList) {
        if (CollectionUtil.isEmpty(workOrderList)) {
            return new ArrayList<>();
        }

        List<DataQualityIssue> issues = new ArrayList<>();

        for (WorkOrderData workOrder : workOrderList) {
            // 检查必需字段
            issues.addAll(checkRequiredFields(workOrder));
            
            // 检查数据一致性
            issues.addAll(checkDataConsistency(workOrder));
            
            // 检查逻辑错误
            issues.addAll(checkLogicalErrors(workOrder));
        }

        log.info("数据质量检查完成，发现 {} 个质量问题", issues.size());
        return issues;
    }

    /**
     * 检查必需字段
     *
     * @param workOrder 工单数据
     * @return 字段缺失问题列表
     */
    private static List<DataQualityIssue> checkRequiredFields(WorkOrderData workOrder) {
        List<DataQualityIssue> issues = new ArrayList<>();

        // 检查工单号
        if (StrUtil.isBlank(workOrder.getOrderNumber())) {
            DataQualityIssue issue = new DataQualityIssue();
            issue.setWorkOrder(workOrder);
            issue.setIssueType(DataQualityIssueType.MISSING_REQUIRED_FIELD);
            issue.setFieldName("orderNumber");
            issue.setDescription("工单号为空");
            issue.setSuggestion("请确保每个工单都有唯一的工单号");
            issues.add(issue);
        }

        // 检查申请人
        if (StrUtil.isBlank(workOrder.getApplicant())) {
            DataQualityIssue issue = new DataQualityIssue();
            issue.setWorkOrder(workOrder);
            issue.setIssueType(DataQualityIssueType.MISSING_REQUIRED_FIELD);
            issue.setFieldName("applicant");
            issue.setDescription("申请人为空");
            issue.setSuggestion("请填写工单申请人信息");
            issues.add(issue);
        }

        // 检查创建日期
        if (workOrder.getCreateDate() == null) {
            DataQualityIssue issue = new DataQualityIssue();
            issue.setWorkOrder(workOrder);
            issue.setIssueType(DataQualityIssueType.MISSING_REQUIRED_FIELD);
            issue.setFieldName("createDate");
            issue.setDescription("创建日期为空");
            issue.setSuggestion("请确保工单有正确的创建日期");
            issues.add(issue);
        }

        return issues;
    }

    /**
     * 检查数据一致性
     *
     * @param workOrder 工单数据
     * @return 数据一致性问题列表
     */
    private static List<DataQualityIssue> checkDataConsistency(WorkOrderData workOrder) {
        List<DataQualityIssue> issues = new ArrayList<>();

        // 检查工作时长与实际时间的一致性（允许较大误差，只检查明显错误）
        if (workOrder.getWorkDurationMinutes() != null && 
            workOrder.getActualStartTime() != null && 
            workOrder.getActualEndTime() != null) {
            
            long actualDuration = ChronoUnit.MINUTES.between(
                    workOrder.getActualStartTime(), workOrder.getActualEndTime());
            
            // 只有差异超过1小时才认为是数据质量问题
            if (Math.abs(actualDuration - workOrder.getWorkDurationMinutes()) > 60) {
                DataQualityIssue issue = new DataQualityIssue();
                issue.setWorkOrder(workOrder);
                issue.setIssueType(DataQualityIssueType.DATA_INCONSISTENCY);
                issue.setFieldName("workDurationMinutes");
                issue.setDescription(String.format("记录的工作时长(%d分钟)与实际时间(%d分钟)不一致", 
                        workOrder.getWorkDurationMinutes(), actualDuration));
                issue.setSuggestion("请检查并更正工作时长记录");
                issues.add(issue);
            }
        }

        return issues;
    }

    /**
     * 检查逻辑错误
     *
     * @param workOrder 工单数据
     * @return 逻辑错误问题列表
     */
    private static List<DataQualityIssue> checkLogicalErrors(WorkOrderData workOrder) {
        List<DataQualityIssue> issues = new ArrayList<>();

        // 检查开始时间晚于结束时间
        if (workOrder.getActualStartTime() != null && workOrder.getActualEndTime() != null) {
            if (workOrder.getActualStartTime().isAfter(workOrder.getActualEndTime())) {
                DataQualityIssue issue = new DataQualityIssue();
                issue.setWorkOrder(workOrder);
                issue.setIssueType(DataQualityIssueType.LOGICAL_ERROR);
                issue.setFieldName("actualStartTime,actualEndTime");
                issue.setDescription("实际开始时间晚于实际结束时间");
                issue.setSuggestion("请检查并更正时间记录");
                issues.add(issue);
            }
        }

        // 检查预估时间逻辑
        if (workOrder.getEstimatedStartTime() != null && workOrder.getEstimatedEndTime() != null) {
            if (workOrder.getEstimatedStartTime().isAfter(workOrder.getEstimatedEndTime())) {
                DataQualityIssue issue = new DataQualityIssue();
                issue.setWorkOrder(workOrder);
                issue.setIssueType(DataQualityIssueType.LOGICAL_ERROR);
                issue.setFieldName("estimatedStartTime,estimatedEndTime");
                issue.setDescription("预估开始时间晚于预估结束时间");
                issue.setSuggestion("请检查并更正预估时间");
                issues.add(issue);
            }
        }

        return issues;
    }

    /**
     * 生成数据质量报告摘要
     *
     * @param issues 数据质量问题列表
     * @return 质量报告摘要
     */
    public static String generateQualityReportSummary(List<DataQualityIssue> issues) {
        if (CollectionUtil.isEmpty(issues)) {
            return "数据质量良好，未发现明显问题。";
        }

        StringBuilder summary = new StringBuilder();
        summary.append(String.format("发现 %d 个数据质量问题：\n", issues.size()));

        // 按问题类型统计
        long missingFieldCount = issues.stream()
                .filter(issue -> issue.getIssueType() == DataQualityIssueType.MISSING_REQUIRED_FIELD)
                .count();
        long inconsistencyCount = issues.stream()
                .filter(issue -> issue.getIssueType() == DataQualityIssueType.DATA_INCONSISTENCY)
                .count();
        long logicalErrorCount = issues.stream()
                .filter(issue -> issue.getIssueType() == DataQualityIssueType.LOGICAL_ERROR)
                .count();

        if (missingFieldCount > 0) {
            summary.append(String.format("- 必需字段缺失：%d 个\n", missingFieldCount));
        }
        if (inconsistencyCount > 0) {
            summary.append(String.format("- 数据不一致：%d 个\n", inconsistencyCount));
        }
        if (logicalErrorCount > 0) {
            summary.append(String.format("- 逻辑错误：%d 个\n", logicalErrorCount));
        }

        summary.append("\n建议优先处理逻辑错误和必需字段缺失问题。");

        return summary.toString();
    }
}

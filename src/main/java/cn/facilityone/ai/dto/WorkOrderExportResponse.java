package cn.facilityone.ai.dto;

import lombok.Data;

import java.util.List;

/**
 * 工单导出响应包装类
 */
@Data
public class WorkOrderExportResponse {

    /**
     * 响应码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 工单导出数据列表
     */
    private List<WorkOrderExportModel> data;

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }
}

package cn.facilityone.ai.dto;

import lombok.Data;

/**
 * 工单导出响应包装类
 */
@Data
public class WorkOrderExportResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 响应码
     */
    private String code;

    /**
     * 状态
     */
    private String status;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 工单导出数据
     */
    private WorkOrderExportData data;

    /**
     * FM系统响应码
     */
    private Integer fmCode;

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(success) && "200".equals(code);
    }
}

package cn.facilityone.ai.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.tool.annotation.ToolParam;

/**
 * 工单导出DTO
 * 用于根据日期范围获取所有工单数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FmWorkOrderExportDTO {

    /**
     * 搜索人ID
     */
    @ToolParam(required = true, description = "搜索人ID")
    private Long userId;

    /**
     * 匹配的项目ID
     */
    @ToolParam(required = true, description = "匹配的项目ID")
    private Long projectId;

    /**
     * 开始日期
     * 格式：YYYY-MM-DD，例如：2025-01-01
     */
    @ToolParam(required = true, description = "开始日期，格式：YYYY-MM-DD，例如：2025-01-01")
    private String startDate;

    /**
     * 结束日期
     * 格式：YYYY-MM-DD，例如：2025-01-31
     */
    @ToolParam(required = true, description = "结束日期，格式：YYYY-MM-DD，例如：2025-01-31")
    private String endDate;
}

package cn.facilityone.ai.dto;

import lombok.Data;

import java.util.Map;

/**
 * 工单导出模型
 * 对应后端接口返回的工单导出数据结构
 */
@Data
public class WorkOrderExportModel {

    /**
     * 单元格样式映射
     */
    private Map<String, Object> cellStyleMap;

    private Long id;

    /**
     * 工单号
     */
    private String code;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 申请人
     */
    private String requestName;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 创建日期
     */
    private String createdDate;

    /**
     * 创建时间
     */
    private String createdTime;

    /**
     * 发布人
     */
    private String publisher;

    /**
     * 发布日期
     */
    private String pubDate;

    /**
     * 发布时间
     */
    private String pubTime;

    /**
     * 执行人
     */
    private String laborer;

    /**
     * 工作团队名称
     */
    private String workTeamName;

    /**
     * 接单日期
     */
    private String acceptDate;

    /**
     * 接单时间
     */
    private String acceptTime;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 故障类型名称
     */
    private String faultTypeName;

    /**
     * 故障原因名称
     */
    private String faultReasonName;

    /**
     * 维修动作名称
     */
    private String repairActionName;

    /**
     * 优先级
     */
    private String priority;

    /**
     * 部门
     */
    private String organization;

    /**
     * 区域
     */
    private String site;

    /**
     * 大厦
     */
    private String building;

    /**
     * 楼层
     */
    private String floor;

    /**
     * 房间
     */
    private String room;

    /**
     * 完整位置
     */
    private String fullPosition;

    /**
     * 描述
     */
    private String description;

    /**
     * 缴费项
     */
    private Integer paymentItem;

    /**
     * 工单状态
     */
    private String status;

    /**
     * 暂停原因
     */
    private String pauseReasons;

    /**
     * 是否收费
     */
    private String isCharged;

    /**
     * 收费金额
     */
    private Double feeAmount;

    /**
     * 预估开始日期
     */
    private String estimatedArriveDate;

    /**
     * 预估开始时间
     */
    private String estimatedArriveTime;

    /**
     * 预估完成日期
     */
    private String estimatedCompleteDate;

    /**
     * 预估完成时间
     */
    private String estimatedCompleteTime;

    /**
     * 预计工作时长
     */
    private Long estimatedWorkingTime;

    /**
     * 实际开始日期
     */
    private String actualArrivalDate;

    /**
     * 实际开始时间
     */
    private String actualArrivalTime;

    /**
     * 实际完成日期
     */
    private String actualCompleteDate;

    /**
     * 实际完成时间
     */
    private String actualCompleteTime;

    /**
     * 实际工作时长
     */
    private Long actualWorkingTime;

    /**
     * 工单暂停时长
     */
    private Long pauseTime;

    /**
     * 工作内容
     */
    private String workcontent;

    /**
     * 需求编号
     */
    private String reqCode;

    /**
     * 服务质量
     */
    private Integer quality;

    /**
     * 服务态度
     */
    private Integer attitude;

    /**
     * 服务速度
     */
    private Integer speed;

    /**
     * 满意度评价内容
     */
    private String satisficactionContent;
}
